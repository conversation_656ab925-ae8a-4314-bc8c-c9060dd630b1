import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Paper,
  Card,
  CardContent,
  Stack,
  Grid,
  Avatar,
  Chip,
  LinearProgress,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useThirdWebMLM } from '../context/ThirdWebMLMContext';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import GroupsIcon from '@mui/icons-material/Groups';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import ShareIcon from '@mui/icons-material/Share';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import DiamondIcon from '@mui/icons-material/Diamond';

const MLMDashboard: React.FC = () => {
  const {
    isConnected,
    connectWallet,
    isMLMRegistered,
    address,
    getDirectReferralCount,
    getDirectReferrals,
    getReferrer
  } = useThirdWebMLM();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Animation states
  const [showContent, setShowContent] = useState<boolean>(false);

  // Dashboard data state
  const [dashboardData, setDashboardData] = useState({
    totalEarnings: 0,
    monthlyEarnings: 0,
    teamSize: 0,
    directReferrals: 0,
    level: 'Bronze',
    nextLevelProgress: 0,
    recentReferrals: [] as Array<{ address: string; date: string; earnings: number }>
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setTimeout(() => setShowContent(true), 300);
  }, []);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!isConnected || !isMLMRegistered || !address) return;

      try {
        setLoading(true);

        // Get real data from contract
        const directReferralCount = await getDirectReferralCount();
        const directReferrals = await getDirectReferrals();
        const referrer = await getReferrer();

        // Calculate level based on referral count
        let level = 'Bronze';
        let nextLevelProgress = 0;

        if (directReferralCount >= 20) {
          level = 'Diamond';
          nextLevelProgress = 100;
        } else if (directReferralCount >= 10) {
          level = 'Gold';
          nextLevelProgress = Math.min(100, (directReferralCount / 20) * 100);
        } else if (directReferralCount >= 5) {
          level = 'Silver';
          nextLevelProgress = Math.min(100, (directReferralCount / 10) * 100);
        } else {
          level = 'Bronze';
          nextLevelProgress = Math.min(100, (directReferralCount / 5) * 100);
        }

        // Mock earnings data (in real implementation, this would come from contract events)
        const totalEarnings = directReferralCount * 0.1; // Mock calculation
        const monthlyEarnings = directReferralCount * 0.05; // Mock calculation

        setDashboardData({
          totalEarnings,
          monthlyEarnings,
          teamSize: directReferralCount, // For simplicity, using direct referrals as team size
          directReferrals: directReferralCount,
          level,
          nextLevelProgress,
          recentReferrals: directReferrals.slice(0, 3).map((addr, index) => ({
            address: `${addr.substring(0, 6)}...${addr.substring(addr.length - 4)}`,
            date: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            earnings: 0.1 // Mock earnings
          }))
        });
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [isConnected, isMLMRegistered, address, getDirectReferralCount, getDirectReferrals, getReferrer]);

  // Redirect if not connected or registered
  useEffect(() => {
    if (!isConnected) {
      navigate('/mlm');
    }
  }, [isConnected, navigate]);

  const cardStyle = {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    transition: 'transform 0.3s, box-shadow 0.3s',
    '&:hover': {
      transform: 'translateY(-5px)',
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Bronze': return '#CD7F32';
      case 'Silver': return '#C0C0C0';
      case 'Gold': return '#FFD700';
      case 'Diamond': return '#B9F2FF';
      default: return '#C0C0C0';
    }
  };

  if (!isConnected) {
    return null; // Will redirect
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in={showContent} timeout={1000}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom sx={{ color: '#1b5e20', fontWeight: 600 }}>
            MLM Dashboard
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Welcome back! Here's your network overview.
          </Typography>
        </Box>
      </Fade>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Earnings */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '100ms' }}>
            <Card sx={{
              ...cardStyle,
              background: 'linear-gradient(135deg, rgba(27, 94, 32, 0.05) 0%, rgba(46, 125, 50, 0.1) 100%)',
              border: '2px solid rgba(46, 125, 50, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <MonetizationOnIcon sx={{ color: '#1b5e20', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#1b5e20' }}>Total Earnings</Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1b5e20' }}>
                  {dashboardData.totalEarnings} ETH
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All-time earnings
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>

        {/* Monthly Earnings */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '200ms' }}>
            <Card sx={{
              ...cardStyle,
              background: 'linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 215, 0, 0.1) 100%)',
              border: '2px solid rgba(255, 193, 7, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TrendingUpIcon sx={{ color: '#FFA000', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#FFA000' }}>This Month</Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#FFA000' }}>
                  {dashboardData.monthlyEarnings} ETH
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Monthly earnings
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>

        {/* Team Size */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '300ms' }}>
            <Card sx={{
              ...cardStyle,
              background: 'linear-gradient(135deg, rgba(103, 58, 183, 0.05) 0%, rgba(63, 81, 181, 0.1) 100%)',
              border: '2px solid rgba(103, 58, 183, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <GroupsIcon sx={{ color: '#673AB7', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#673AB7' }}>Team Size</Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#673AB7' }}>
                  {dashboardData.teamSize}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total team members
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>

        {/* Direct Referrals */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '400ms' }}>
            <Card sx={{
              ...cardStyle,
              background: 'linear-gradient(135deg, rgba(0, 188, 212, 0.05) 0%, rgba(3, 169, 244, 0.1) 100%)',
              border: '2px solid rgba(0, 188, 212, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PersonAddIcon sx={{ color: '#00BCD4', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#00BCD4' }}>Direct Referrals</Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#00BCD4' }}>
                  {dashboardData.directReferrals}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Your direct referrals
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Level Progress */}
        <Grid item xs={12} md={6}>
          <Fade in={showContent} timeout={1500}>
            <Card sx={cardStyle}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <DiamondIcon sx={{ color: getLevelColor(dashboardData.level), mr: 1 }} />
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Current Level
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label={dashboardData.level}
                    sx={{ 
                      backgroundColor: getLevelColor(dashboardData.level),
                      color: 'white',
                      fontWeight: 'bold',
                      mr: 2
                    }}
                  />
                  <Typography variant="body1">
                    Progress to Gold: {dashboardData.nextLevelProgress}%
                  </Typography>
                </Box>

                <LinearProgress 
                  variant="determinate" 
                  value={dashboardData.nextLevelProgress}
                  sx={{ 
                    height: 10, 
                    borderRadius: 5,
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#FFD700'
                    }
                  }}
                />

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  Invite 3 more members to reach Gold level
                </Typography>
              </CardContent>
            </Card>
          </Fade>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Fade in={showContent} timeout={1500}>
            <Card sx={cardStyle}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
                  Quick Actions
                </Typography>
                
                <Stack spacing={2}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<ShareIcon />}
                    sx={{
                      py: 1.5,
                      backgroundColor: '#1b5e20',
                      '&:hover': { backgroundColor: '#2e7d32' }
                    }}
                  >
                    Share Referral Link
                  </Button>
                  
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<AccountTreeIcon />}
                    sx={{
                      py: 1.5,
                      borderColor: '#1b5e20',
                      color: '#1b5e20',
                      '&:hover': { 
                        borderColor: '#2e7d32',
                        backgroundColor: 'rgba(27, 94, 32, 0.1)'
                      }
                    }}
                  >
                    View Network Tree
                  </Button>
                  
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<EmojiEventsIcon />}
                    sx={{
                      py: 1.5,
                      borderColor: '#FFA000',
                      color: '#FFA000',
                      '&:hover': { 
                        borderColor: '#FF8F00',
                        backgroundColor: 'rgba(255, 160, 0, 0.1)'
                      }
                    }}
                  >
                    View Rewards
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Fade>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12}>
          <Fade in={showContent} timeout={2000}>
            <Card sx={cardStyle}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
                  Recent Referral Activity
                </Typography>
                
                {dashboardData.recentReferrals.map((referral, index) => (
                  <Box key={index}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: '#1b5e20', mr: 2 }}>
                          <PersonAddIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            New Referral: {referral.address}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {referral.date}
                          </Typography>
                        </Box>
                      </Box>
                      <Chip 
                        label={`+${referral.earnings} ETH`}
                        color="success"
                        variant="outlined"
                      />
                    </Box>
                    {index < dashboardData.recentReferrals.length - 1 && <Divider />}
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Fade>
        </Grid>
      </Grid>
    </Container>
  );
};

export default MLMDashboard;

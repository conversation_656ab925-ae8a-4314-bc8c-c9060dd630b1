import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Paper,
  Card,
  CardContent,
  Stack,
  Grid,
  Avatar,
  Chip,
  LinearProgress,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useMining } from '../context/MiningContext';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import ShareIcon from '@mui/icons-material/Share';
import DiamondIcon from '@mui/icons-material/Diamond';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

// Add mining animation styles
const miningAnimationStyles = `
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
  }

  @keyframes mining {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
    100% { transform: translateY(0px); }
  }

  .mining-animation {
    animation: mining 2s ease-in-out infinite;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = miningAnimationStyles;
  document.head.appendChild(styleSheet);
}

const MLMDashboard: React.FC = () => {
  const {
    isConnected,
    isRegistered,
    address,
    userRecord,
    dailyReward,
    regReward,
    directReferrals,
    directReferralCount,
    referrer,
    totalRegistered,
    canClaim,
    timeUntilNextClaim,
    claimDailyReward,
    refreshData
  } = useMining();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Animation states
  const [showContent, setShowContent] = useState<boolean>(false);

  // Dashboard data state
  const [dashboardData, setDashboardData] = useState({
    totalMinted: 0,
    dailyReward: 0,
    regReward: 0,
    teamSize: 0,
    directReferrals: 0,
    level: 'Bronze',
    nextLevelProgress: 0,
    recentReferrals: [] as Array<{ address: string; date: string; earnings: number }>
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setTimeout(() => setShowContent(true), 300);
  }, []);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!isConnected || !isRegistered || !address) return;

      try {
        setLoading(true);

        // Calculate level based on referral count
        let level = 'Bronze';
        let nextLevelProgress = 0;

        if (directReferralCount >= 20) {
          level = 'Diamond';
          nextLevelProgress = 100;
        } else if (directReferralCount >= 10) {
          level = 'Gold';
          nextLevelProgress = Math.min(100, (directReferralCount / 20) * 100);
        } else if (directReferralCount >= 5) {
          level = 'Silver';
          nextLevelProgress = Math.min(100, (directReferralCount / 10) * 100);
        } else {
          level = 'Bronze';
          nextLevelProgress = Math.min(100, (directReferralCount / 5) * 100);
        }

        // Convert BigInt values to numbers for display
        const totalMinted = userRecord ? Number(userRecord.totalMinted) / 1e18 : 0;
        const dailyRewardValue = Number(dailyReward) / 1e18;
        const regRewardValue = Number(regReward) / 1e18;

        setDashboardData({
          totalMinted,
          dailyReward: dailyRewardValue,
          regReward: regRewardValue,
          teamSize: directReferralCount, // For simplicity, using direct referrals as team size
          directReferrals: directReferralCount,
          level,
          nextLevelProgress,
          recentReferrals: directReferrals.slice(0, 3).map((addr, index) => ({
            address: `${addr.substring(0, 6)}...${addr.substring(addr.length - 4)}`,
            date: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            earnings: regRewardValue // Use registration reward as earnings
          }))
        });
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [isConnected, isRegistered, address, directReferralCount, directReferrals, userRecord, dailyReward, regReward]);

  // Redirect if not connected or registered
  useEffect(() => {
    if (!isConnected) {
      navigate('/mining');
    }
  }, [isConnected, navigate]);

  const cardStyle = {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    transition: 'transform 0.3s, box-shadow 0.3s',
    '&:hover': {
      transform: 'translateY(-5px)',
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Bronze': return '#CD7F32';
      case 'Silver': return '#C0C0C0';
      case 'Gold': return '#FFD700';
      case 'Diamond': return '#B9F2FF';
      default: return '#C0C0C0';
    }
  };

  if (!isConnected) {
    return null; // Will redirect
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Fade in={showContent} timeout={1000}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom sx={{ color: '#1b5e20', fontWeight: 600 }}>
            Mining Dashboard
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Welcome back! Here's your mining overview.
          </Typography>
        </Box>
      </Fade>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Minted */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '100ms' }}>
            <Card sx={{
              ...cardStyle,
              background: 'linear-gradient(135deg, rgba(27, 94, 32, 0.05) 0%, rgba(46, 125, 50, 0.1) 100%)',
              border: '2px solid rgba(46, 125, 50, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <MonetizationOnIcon sx={{ color: '#1b5e20', mr: 1 }} className="mining-animation" />
                  <Typography variant="h6" sx={{ color: '#1b5e20' }}>Total Minted</Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1b5e20' }}>
                  {dashboardData.totalMinted.toFixed(4)} Tokens
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All-time minted tokens
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>

        {/* Daily Reward */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '200ms' }}>
            <Card sx={{
              ...cardStyle,
              background: 'linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 215, 0, 0.1) 100%)',
              border: '2px solid rgba(255, 193, 7, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TrendingUpIcon sx={{ color: '#FFA000', mr: 1 }} className="mining-animation" />
                  <Typography variant="h6" sx={{ color: '#FFA000' }}>Daily Reward</Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#FFA000' }}>
                  {dashboardData.dailyReward.toFixed(4)} Tokens
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Daily mining reward
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>

        {/* Mining Status */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '300ms' }}>
            <Card sx={{
              ...cardStyle,
              background: canClaim
                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(139, 195, 74, 0.1) 100%)'
                : 'linear-gradient(135deg, rgba(103, 58, 183, 0.05) 0%, rgba(63, 81, 181, 0.1) 100%)',
              border: canClaim
                ? '2px solid rgba(76, 175, 80, 0.2)'
                : '2px solid rgba(103, 58, 183, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AccessTimeIcon sx={{ color: canClaim ? '#4CAF50' : '#673AB7', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: canClaim ? '#4CAF50' : '#673AB7' }}>
                    Mining Status
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: canClaim ? '#4CAF50' : '#673AB7' }}>
                  {canClaim ? 'Ready!' : `${Math.floor(timeUntilNextClaim / 60)}:${(timeUntilNextClaim % 60).toString().padStart(2, '0')}`}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {canClaim ? 'Claim available' : 'Next claim in'}
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>

        {/* Direct Referrals */}
        <Grid item xs={12} sm={6} md={3}>
          <Zoom in={showContent} timeout={1000} style={{ transitionDelay: '400ms' }}>
            <Card sx={{
              ...cardStyle,
              background: 'linear-gradient(135deg, rgba(0, 188, 212, 0.05) 0%, rgba(3, 169, 244, 0.1) 100%)',
              border: '2px solid rgba(0, 188, 212, 0.2)',
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PersonAddIcon sx={{ color: '#00BCD4', mr: 1 }} />
                  <Typography variant="h6" sx={{ color: '#00BCD4' }}>Direct Referrals</Typography>
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#00BCD4' }}>
                  {dashboardData.directReferrals}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Your direct referrals
                </Typography>
              </CardContent>
            </Card>
          </Zoom>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Mining Progress */}
        <Grid item xs={12} md={6}>
          <Fade in={showContent} timeout={1500}>
            <Card sx={cardStyle}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <DiamondIcon sx={{ color: getLevelColor(dashboardData.level), mr: 1 }} />
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Mining Level
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip
                    label={dashboardData.level}
                    sx={{
                      backgroundColor: getLevelColor(dashboardData.level),
                      color: 'white',
                      fontWeight: 'bold',
                      mr: 2
                    }}
                  />
                  <Typography variant="body1">
                    Progress to next level: {dashboardData.nextLevelProgress.toFixed(1)}%
                  </Typography>
                </Box>

                <LinearProgress
                  variant="determinate"
                  value={dashboardData.nextLevelProgress}
                  sx={{
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getLevelColor(dashboardData.level),
                      animation: 'pulse 2s infinite'
                    }
                  }}
                />

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  {dashboardData.level === 'Diamond'
                    ? 'Maximum level reached!'
                    : `Refer ${dashboardData.level === 'Bronze' ? 5 - dashboardData.directReferrals :
                        dashboardData.level === 'Silver' ? 10 - dashboardData.directReferrals :
                        20 - dashboardData.directReferrals} more miners to advance`}
                </Typography>
              </CardContent>
            </Card>
          </Fade>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Fade in={showContent} timeout={1500}>
            <Card sx={cardStyle}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
                  Mining Actions
                </Typography>

                <Stack spacing={2}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<PlayArrowIcon />}
                    disabled={!canClaim || loading}
                    onClick={async () => {
                      setLoading(true);
                      try {
                        await claimDailyReward();
                        await refreshData();
                      } catch (error) {
                        console.error('Error claiming reward:', error);
                      } finally {
                        setLoading(false);
                      }
                    }}
                    sx={{
                      py: 1.5,
                      backgroundColor: canClaim ? '#4CAF50' : '#9E9E9E',
                      '&:hover': { backgroundColor: canClaim ? '#66BB6A' : '#9E9E9E' },
                      '&:disabled': { backgroundColor: '#9E9E9E' }
                    }}
                  >
                    {canClaim ? 'Claim Daily Reward' : `Next Claim: ${Math.floor(timeUntilNextClaim / 60)}:${(timeUntilNextClaim % 60).toString().padStart(2, '0')}`}
                  </Button>

                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<ShareIcon />}
                    sx={{
                      py: 1.5,
                      borderColor: '#1b5e20',
                      color: '#1b5e20',
                      '&:hover': {
                        borderColor: '#2e7d32',
                        backgroundColor: 'rgba(27, 94, 32, 0.1)'
                      }
                    }}
                  >
                    Share Referral Link
                  </Button>

                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<AccountTreeIcon />}
                    sx={{
                      py: 1.5,
                      borderColor: '#673AB7',
                      color: '#673AB7',
                      '&:hover': {
                        borderColor: '#7E57C2',
                        backgroundColor: 'rgba(103, 58, 183, 0.1)'
                      }
                    }}
                  >
                    View Referral Network
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Fade>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12}>
          <Fade in={showContent} timeout={2000}>
            <Card sx={cardStyle}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
                  Recent Mining Activity
                </Typography>

                {dashboardData.recentReferrals.length > 0 ? (
                  dashboardData.recentReferrals.map((referral, index) => (
                    <Box key={index}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ bgcolor: '#1b5e20', mr: 2 }}>
                            <PersonAddIcon />
                          </Avatar>
                          <Box>
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              New Referral: {referral.address}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {referral.date}
                            </Typography>
                          </Box>
                        </Box>
                        <Chip
                          label={`+${referral.earnings.toFixed(4)} Tokens`}
                          color="success"
                          variant="outlined"
                        />
                      </Box>
                      {index < dashboardData.recentReferrals.length - 1 && <Divider />}
                    </Box>
                  ))
                ) : (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      No recent activity. Start mining and referring to see activity here!
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Fade>
        </Grid>
      </Grid>
    </Container>
  );
};

export default MLMDashboard;

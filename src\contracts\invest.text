/**
 *Submitted for verification at testnet.bscscan.com on 2025-06-03
*/

// SPDX-License-Identifier: MIT
// File: @openzeppelin/contracts/token/ERC20/IERC20.sol

// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/IERC20.sol)

pragma solidity ^0.8.20;

/**
 * @dev Interface of the ERC-20 standard as defined in the ERC.
 */
interface IERC20 {
    /**
     * @dev Emitted when `value` tokens are moved from one account (`from`) to
     * another (`to`).
     *
     * Note that `value` may be zero.
     */
    event Transfer(address indexed from, address indexed to, uint256 value);

    /**
     * @dev Emitted when the allowance of a `spender` for an `owner` is set by
     * a call to {approve}. `value` is the new allowance.
     */
    event Approval(
        address indexed owner,
        address indexed spender,
        uint256 value
    );

    /**
     * @dev Returns the value of tokens in existence.
     */
    function totalSupply() external view returns (uint256);

    /**
     * @dev Returns the value of tokens owned by `account`.
     */
    function balanceOf(address account) external view returns (uint256);

    /**
     * @dev Moves a `value` amount of tokens from the caller's account to `to`.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transfer(address to, uint256 value) external returns (bool);

    /**
     * @dev Returns the remaining number of tokens that `spender` will be
     * allowed to spend on behalf of `owner` through {transferFrom}. This is
     * zero by default.
     *
     * This value changes when {approve} or {transferFrom} are called.
     */
    function allowance(address owner, address spender)
        external
        view
        returns (uint256);

    /**
     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the
     * caller's tokens.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * IMPORTANT: Beware that changing an allowance with this method brings the risk
     * that someone may use both the old and the new allowance by unfortunate
     * transaction ordering. One possible solution to mitigate this race
     * condition is to first reduce the spender's allowance to 0 and set the
     * desired value afterwards:
     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729
     *
     * Emits an {Approval} event.
     */
    function approve(address spender, uint256 value) external returns (bool);

    /**
     * @dev Moves a `value` amount of tokens from `from` to `to` using the
     * allowance mechanism. `value` is then deducted from the caller's
     * allowance.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transferFrom(
        address from,
        address to,
        uint256 value
    ) external returns (bool);
}

interface IReferralRegistry {
    function getReferrerOf(address _user) external view returns (address);

    function checkIfRegistered(address _user) external view returns (bool);

    function totalRegistered() external view returns (uint256);

    function getAllRegistered() external view returns (address[] memory);
}
// File: @openzeppelin/contracts/utils/Context.sol

// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)

pragma solidity ^0.8.20;

/**
 * @dev Provides information about the current execution context, including the
 * sender of the transaction and its data. While these are generally available
 * via msg.sender and msg.data, they should not be accessed in such a direct
 * manner, since when dealing with meta-transactions the account sending and
 * paying for execution may not be the actual sender (as far as an application
 * is concerned).
 *
 * This contract is only required for intermediate, library-like contracts.
 */
abstract contract Context {
    function _msgSender() internal view virtual returns (address) {
        return msg.sender;
    }

    function _msgData() internal view virtual returns (bytes calldata) {
        return msg.data;
    }

    function _contextSuffixLength() internal view virtual returns (uint256) {
        return 0;
    }
}

// File: @openzeppelin/contracts/access/Ownable.sol

// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)

pragma solidity ^0.8.20;

/**
 * @dev Contract module which provides a basic access control mechanism, where
 * there is an account (an owner) that can be granted exclusive access to
 * specific functions.
 *
 * The initial owner is set to the address provided by the deployer. This can
 * later be changed with {transferOwnership}.
 *
 * This module is used through inheritance. It will make available the modifier
 * `onlyOwner`, which can be applied to your functions to restrict their use to
 * the owner.
 */
abstract contract Ownable is Context {
    address private _owner;

    /**
     * @dev The caller account is not authorized to perform an operation.
     */
    error OwnableUnauthorizedAccount(address account);

    /**
     * @dev The owner is not a valid owner account. (eg. `address(0)`)
     */
    error OwnableInvalidOwner(address owner);

    event OwnershipTransferred(
        address indexed previousOwner,
        address indexed newOwner
    );

    /**
     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.
     */
    constructor(address initialOwner) {
        if (initialOwner == address(0)) {
            revert OwnableInvalidOwner(address(0));
        }
        _transferOwnership(initialOwner);
    }

    /**
     * @dev Throws if called by any account other than the owner.
     */
    modifier onlyOwner() {
        _checkOwner();
        _;
    }

    /**
     * @dev Returns the address of the current owner.
     */
    function owner() public view virtual returns (address) {
        return _owner;
    }

    /**
     * @dev Throws if the sender is not the owner.
     */
    function _checkOwner() internal view virtual {
        if (owner() != _msgSender()) {
            revert OwnableUnauthorizedAccount(_msgSender());
        }
    }

    /**
     * @dev Leaves the contract without owner. It will not be possible to call
     * `onlyOwner` functions. Can only be called by the current owner.
     *
     * NOTE: Renouncing ownership will leave the contract without an owner,
     * thereby disabling any functionality that is only available to the owner.
     */
    function renounceOwnership() public virtual onlyOwner {
        _transferOwnership(address(0));
    }

    /**
     * @dev Transfers ownership of the contract to a new account (`newOwner`).
     * Can only be called by the current owner.
     */
    function transferOwnership(address newOwner) public virtual onlyOwner {
        if (newOwner == address(0)) {
            revert OwnableInvalidOwner(address(0));
        }
        _transferOwnership(newOwner);
    }

    /**
     * @dev Transfers ownership of the contract to a new account (`newOwner`).
     * Internal function without access restriction.
     */
    function _transferOwnership(address newOwner) internal virtual {
        address oldOwner = _owner;
        _owner = newOwner;
        emit OwnershipTransferred(oldOwner, newOwner);
    }
}

// File: @openzeppelin/contracts/utils/ReentrancyGuard.sol

// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)

pragma solidity ^0.8.20;

/**
 * @dev Contract module that helps prevent reentrant calls to a function.
 *
 * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier
 * available, which can be applied to functions to make sure there are no nested
 * (reentrant) calls to them.
 *
 * Note that because there is a single `nonReentrant` guard, functions marked as
 * `nonReentrant` may not call one another. This can be worked around by making
 * those functions `private`, and then adding `external` `nonReentrant` entry
 * points to them.
 *
 * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,
 * consider using {ReentrancyGuardTransient} instead.
 *
 * TIP: If you would like to learn more about reentrancy and alternative ways
 * to protect against it, check out our blog post
 * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].
 */
abstract contract ReentrancyGuard {
    // Booleans are more expensive than uint256 or any type that takes up a full
    // word because each write operation emits an extra SLOAD to first read the
    // slot's contents, replace the bits taken up by the boolean, and then write
    // back. This is the compiler's defense against contract upgrades and
    // pointer aliasing, and it cannot be disabled.

    // The values being non-zero value makes deployment a bit more expensive,
    // but in exchange the refund on every call to nonReentrant will be lower in
    // amount. Since refunds are capped to a percentage of the total
    // transaction's gas, it is best to keep them low in cases like this one, to
    // increase the likelihood of the full refund coming into effect.
    uint256 private constant NOT_ENTERED = 1;
    uint256 private constant ENTERED = 2;

    uint256 private _status;

    /**
     * @dev Unauthorized reentrant call.
     */
    error ReentrancyGuardReentrantCall();

    constructor() {
        _status = NOT_ENTERED;
    }

    /**
     * @dev Prevents a contract from calling itself, directly or indirectly.
     * Calling a `nonReentrant` function from another `nonReentrant`
     * function is not supported. It is possible to prevent this from happening
     * by making the `nonReentrant` function external, and making it call a
     * `private` function that does the actual work.
     */
    modifier nonReentrant() {
        _nonReentrantBefore();
        _;
        _nonReentrantAfter();
    }

    function _nonReentrantBefore() private {
        // On the first call to nonReentrant, _status will be NOT_ENTERED
        if (_status == ENTERED) {
            revert ReentrancyGuardReentrantCall();
        }

        // Any calls to nonReentrant after this point will fail
        _status = ENTERED;
    }

    function _nonReentrantAfter() private {
        // By storing the original value once again, a refund is triggered (see
        // https://eips.ethereum.org/EIPS/eip-2200)
        _status = NOT_ENTERED;
    }

    /**
     * @dev Returns true if the reentrancy guard is currently set to "entered", which indicates there is a
     * `nonReentrant` function in the call stack.
     */
    function _reentrancyGuardEntered() internal view returns (bool) {
        return _status == ENTERED;
    }
}

// File: contracts/newContractForPool.sol

pragma solidity ^0.8.20;

/**
 * @title TokenVault
 * @dev A contract for gasless platform where users can lock their tokens for different durations to participate in a points-based system.
 *      Blacklisted users are prevented from participating. Admin can blacklist users and control certain aspects.
 */
contract usdtStaking is Ownable, ReentrancyGuard {
    // Token that users will lock
    IERC20 public usdt;
    IReferralRegistry public referralRegistry;

    uint256[5] rewardPercents = [100_00, 50_00, 10_00, 10_00, 10_00];

    uint256 public percentDivider;
    address[] public allUsers;

    struct User {
        uint256 totalContribution;
        bool isExists;
        uint256 contributionCount;
        uint256 totalClaimedReward;
        uint256 totalLevelIncome;
    }

    struct Pool {
        uint256 minInvestAmount;
        uint256 maxInvestAmount;
        uint256 rewardMultiplier;
        uint256 totalAmountRaised;
        uint256 totalRewardDistributed;
    }

    // Struct to store each contribution made by users
    struct Contribution {
        uint256 planIndex; // Plan of contribution
        uint256 amount; // Amount of tokens locked
        uint256 rewardClaimed; // claimed reward
        uint256 lastClaimTime;
    }

    // Mapping to track all contributions made by a user
    mapping(address => mapping(uint256 => Contribution))
        public userContributions;

    mapping(address => User) public userData;

    // Mapping to keep Pool Data
    mapping(uint256 => Pool) public poolDetails;

    mapping(address => mapping(uint256 => bool)) public boughtPlans;

    /**
     * @dev Constructor to initialize the contract with addresses and set up rewardMultipliers for lock durations.
     */
    constructor(address _usdt, address _referralRegistry)
        Ownable(address(msg.sender))
    {
        usdt = IERC20(_usdt);

        // Define rewardMultipliers for each lock period
        poolDetails[0].rewardMultiplier = 1_00;
        poolDetails[1].rewardMultiplier = 1_20;
        poolDetails[2].rewardMultiplier = 1_35;
        poolDetails[3].rewardMultiplier = 1_50;

        percentDivider = 100_00;

        poolDetails[0].minInvestAmount = 10 * 10**18;
        poolDetails[1].minInvestAmount = 1000 * 10**18;
        poolDetails[2].minInvestAmount = 5000 * 10**18;
        poolDetails[3].minInvestAmount = 10000 * 10**18;

        poolDetails[0].maxInvestAmount = 900 * 10**18;
        poolDetails[1].maxInvestAmount = 4000 * 10**18;
        poolDetails[2].maxInvestAmount = 9000 * 10**18;
        poolDetails[3].maxInvestAmount = 50000 * 10**18;

        userData[msg.sender].totalContribution = 1;
        referralRegistry = IReferralRegistry(_referralRegistry);
    }

    function StaterPackage(uint256 _amount)
        external
        nonReentrant
        returns (address, uint256)
    {
        uint256 _planIndex = 0;
        User storage user = userData[msg.sender];
        Contribution storage userContData = userContributions[msg.sender][
            user.contributionCount
        ];
        require(
            referralRegistry.checkIfRegistered(msg.sender),
            "User not registered"
        );
        Pool storage pool = poolDetails[_planIndex];
        require(
            _amount >= pool.minInvestAmount && _amount <= pool.maxInvestAmount,
            "Amount must be accurate"
        );
        require(pool.rewardMultiplier >= 0, "Invalid plan");
        // Transfer tokens from user to the contract
        usdt.transferFrom(msg.sender, address(this), _amount);

        userContData.planIndex = _planIndex;
        userContData.amount = _amount;
        userContData.lastClaimTime = block.timestamp;
        user.totalContribution += _amount;
        pool.totalAmountRaised += _amount;
        user.contributionCount++;
        boughtPlans[msg.sender][_planIndex] = true;
        return (msg.sender, user.contributionCount - 1);
    }

    function SilverPackage(uint256 _amount)
        external
        nonReentrant
        returns (address, uint256)
    {
        uint256 _planIndex = 1;
        User storage user = userData[msg.sender];
        Contribution storage userContData = userContributions[msg.sender][
            user.contributionCount
        ];
        require(
            referralRegistry.checkIfRegistered(msg.sender),
            "User not registered"
        );
        Pool storage pool = poolDetails[_planIndex];
        require(
            _amount >= pool.minInvestAmount && _amount <= pool.maxInvestAmount,
            "Amount must be accurate"
        );
        require(pool.rewardMultiplier >= 0, "Invalid plan");
        // Transfer tokens from user to the contract
        usdt.transferFrom(msg.sender, address(this), _amount);

        userContData.planIndex = _planIndex;
        userContData.amount = _amount;
        userContData.lastClaimTime = block.timestamp;
        user.totalContribution += _amount;
        pool.totalAmountRaised += _amount;
        user.contributionCount++;
        boughtPlans[msg.sender][_planIndex] = true;
        return (msg.sender, user.contributionCount - 1);
    }

    function GoldPackage(uint256 _amount)
        external
        nonReentrant
        returns (address, uint256)
    {
        uint256 _planIndex = 2;
        User storage user = userData[msg.sender];
        Contribution storage userContData = userContributions[msg.sender][
            user.contributionCount
        ];
        require(
            referralRegistry.checkIfRegistered(msg.sender),
            "User not registered"
        );
        Pool storage pool = poolDetails[_planIndex];
        require(
            _amount >= pool.minInvestAmount && _amount <= pool.maxInvestAmount,
            "Amount must be accurate"
        );
        require(pool.rewardMultiplier >= 0, "Invalid plan");
        // Transfer tokens from user to the contract
        usdt.transferFrom(msg.sender, address(this), _amount);

        userContData.planIndex = _planIndex;
        userContData.amount = _amount;
        userContData.lastClaimTime = block.timestamp;
        user.totalContribution += _amount;
        pool.totalAmountRaised += _amount;
        user.contributionCount++;
        boughtPlans[msg.sender][_planIndex] = true;
        return (msg.sender, user.contributionCount - 1);
    }

    function DiamondPackage(uint256 _amount)
        external
        nonReentrant
        returns (address, uint256)
    {
        uint256 _planIndex = 3;
        User storage user = userData[msg.sender];
        Contribution storage userContData = userContributions[msg.sender][
            user.contributionCount
        ];
        require(
            referralRegistry.checkIfRegistered(msg.sender),
            "User not registered"
        );
        Pool storage pool = poolDetails[_planIndex];
        require(
            _amount >= pool.minInvestAmount && _amount <= pool.maxInvestAmount,
            "Amount must be accurate"
        );
        require(pool.rewardMultiplier >= 0, "Invalid plan");
        // Transfer tokens from user to the contract
        usdt.transferFrom(msg.sender, address(this), _amount);

        userContData.planIndex = _planIndex;
        userContData.amount = _amount;
        userContData.lastClaimTime = block.timestamp;
        user.totalContribution += _amount;
        pool.totalAmountRaised += _amount;
        user.contributionCount++;
        boughtPlans[msg.sender][_planIndex] = true;
        return (msg.sender, user.contributionCount - 1);
    }

    function claimReward(uint256 _index) public nonReentrant {
        User storage user = userData[msg.sender];
        Contribution storage userContData = userContributions[msg.sender][
            _index
        ];
        Pool storage pool = poolDetails[userContData.planIndex];
        require(
            referralRegistry.checkIfRegistered(msg.sender),
            "user has no existence"
        );
        require(_index < user.contributionCount, "Invalid index");
        require(block.timestamp > userContData.lastClaimTime + 1 minutes);
        uint256 reward = calculateReward(msg.sender, _index);
        require(reward > 0, "no reward to claim");
        usdt.transfer(msg.sender, reward);
        address currentUpline = referralRegistry.getReferrerOf(msg.sender);
        for (uint256 i = 0; i < 5; i++) {
            if (currentUpline == address(0)) {
                break;
            }
            uint256 share = (reward * rewardPercents[i]) / percentDivider;
            if (share > 0) {
                usdt.transfer(currentUpline, share);
                userData[currentUpline].totalLevelIncome += share;
            }
            currentUpline = referralRegistry.getReferrerOf(currentUpline); // Go one level up
        }

        pool.totalRewardDistributed += reward; // Adding rewards to total distributed amount of tokens from the contract
        userContData.lastClaimTime = block.timestamp; // Update last claim time for
        userContData.rewardClaimed += reward;
        user.totalClaimedReward += reward;
    }

    function calculateReward(address _user, uint256 _index)
        public
        view
        returns (uint256 _reward)
    {
        Contribution storage userContData = userContributions[_user][_index];
        Pool storage pool = poolDetails[userContData.planIndex];

        uint256 duration = (block.timestamp - userContData.lastClaimTime) /
            1 minutes;

        _reward = ((userContData.amount * pool.rewardMultiplier) /
            percentDivider);

        _reward = _reward * duration;
        if (userContData.rewardClaimed + _reward >= 3 * (userContData.amount)) {
            _reward = (3 * userContData.amount) - userContData.rewardClaimed;
        }
    }

    /**
     * @dev Withdraw tokens from the contract (admin only).
     * @param _token The address of the token to withdraw.
     */
    function liquidityToken(address _token) external onlyOwner {
        IERC20 token = IERC20(_token);
        token.transfer(owner(), token.balanceOf(address(this)));
    }

    /**
     * @dev Withdraw eth from the contract (admin only).
     */
    function withdrawBNB() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }

    /**
     * @dev Retrieve the total user Count
     * @return The total number of user which exists in smart contract
     */
    function getAllUserLenght() public view returns (uint256) {
        return allUsers.length;
    }

    function getUserData(address _user)
        public
        view
        returns (
            uint256 _totalContribution,
            bool _isExists,
            uint256 _contributionCount,
            uint256 _totalClaimedReward,
            uint256 _totalLevelIncome
        )
    {
        User memory user = userData[_user];
        return (
            user.totalContribution,
            user.isExists,
            user.contributionCount,
            user.totalClaimedReward,
            user.totalLevelIncome
        );
    }

    function getUserContribution(address _user, uint256 _index)
        public
        view
        returns (
            uint256 _planIndex, // Plan of contribution
            uint256 _amount, // Amount of tokens locked
            uint256 _rewardClaimed, // claimed reward
            uint256 _lastClaimTime
        )
    {
        Contribution memory userContData = userContributions[_user][_index];
        return (
            userContData.planIndex,
            userContData.amount,
            userContData.rewardClaimed,
            userContData.lastClaimTime
        );
    }

    /**
     * @dev Calculates and returns the total accumulated rewards for a given user
     * across all their contributions.
     *
     * Iterates through each contribution of the user and sums up the rewards
     * calculated by the `calculateReward` function.
     *
     * @param _user The address of the user whose rewards are being calculated.
     * @return totalReward The total reward amount accumulated by the user.
     */
    function getAllCalculatedReward(address _user)
        public
        view
        returns (uint256 totalReward)
    {
        User storage user = userData[_user];
        for (uint256 i = 0; i < user.contributionCount; i++) {
            totalReward += calculateReward(_user, i);
        }
    }
}

abi = [{"inputs":[{"internalType":"address","name":"_usdt","type":"address"},{"internalType":"address","name":"_referralRegistry","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[{"internalType":"address","name":"owner","type":"address"}],"name":"OwnableInvalidOwner","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"OwnableUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},{"inputs":[{"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"DiamondPackage","outputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"GoldPackage","outputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"SilverPackage","outputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"StaterPackage","outputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"allUsers","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"uint256","name":"","type":"uint256"}],"name":"boughtPlans","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_user","type":"address"},{"internalType":"uint256","name":"_index","type":"uint256"}],"name":"calculateReward","outputs":[{"internalType":"uint256","name":"_reward","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"}],"name":"claimReward","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"_user","type":"address"}],"name":"getAllCalculatedReward","outputs":[{"internalType":"uint256","name":"totalReward","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllUserLenght","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_user","type":"address"},{"internalType":"uint256","name":"_index","type":"uint256"}],"name":"getUserContribution","outputs":[{"internalType":"uint256","name":"_planIndex","type":"uint256"},{"internalType":"uint256","name":"_amount","type":"uint256"},{"internalType":"uint256","name":"_rewardClaimed","type":"uint256"},{"internalType":"uint256","name":"_lastClaimTime","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_user","type":"address"}],"name":"getUserData","outputs":[{"internalType":"uint256","name":"_totalContribution","type":"uint256"},{"internalType":"bool","name":"_isExists","type":"bool"},{"internalType":"uint256","name":"_contributionCount","type":"uint256"},{"internalType":"uint256","name":"_totalClaimedReward","type":"uint256"},{"internalType":"uint256","name":"_totalLevelIncome","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_token","type":"address"}],"name":"liquidityToken","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"percentDivider","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"poolDetails","outputs":[{"internalType":"uint256","name":"minInvestAmount","type":"uint256"},{"internalType":"uint256","name":"maxInvestAmount","type":"uint256"},{"internalType":"uint256","name":"rewardMultiplier","type":"uint256"},{"internalType":"uint256","name":"totalAmountRaised","type":"uint256"},{"internalType":"uint256","name":"totalRewardDistributed","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"referralRegistry","outputs":[{"internalType":"contract IReferralRegistry","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"renounceOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"usdt","outputs":[{"internalType":"contract IERC20","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"uint256","name":"","type":"uint256"}],"name":"userContributions","outputs":[{"internalType":"uint256","name":"planIndex","type":"uint256"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"rewardClaimed","type":"uint256"},{"internalType":"uint256","name":"lastClaimTime","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"}],"name":"userData","outputs":[{"internalType":"uint256","name":"totalContribution","type":"uint256"},{"internalType":"bool","name":"isExists","type":"bool"},{"internalType":"uint256","name":"contributionCount","type":"uint256"},{"internalType":"uint256","name":"totalClaimedReward","type":"uint256"},{"internalType":"uint256","name":"totalLevelIncome","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"withdrawBNB","outputs":[],"stateMutability":"nonpayable","type":"function"}]
contract==0x35268DB5c7E4d485b90856eFE1c61c306C0688aE
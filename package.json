{"name": "universalbet", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.16", "@mui/material": "^5.15.16", "@tanstack/react-query": "^4.40.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.0", "thirdweb": "5.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.1", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "eslint": "^8.57.0", "https-browserify": "^1.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "terser": "^5.43.1", "typescript": "^5.4.5", "url": "^0.11.4", "util": "^0.12.5", "vite": "^6.3.5"}, "resolutions": {"@tanstack/react-query": "^4.40.0"}}